# Transport Server Makefile
# 货运服务器构建脚本

APP_NAME = transport-server
BUILD_DIR = build
LINUX_DIR = $(BUILD_DIR)/linux

# Go 构建参数
GOOS = linux
GOARCH = amd64
CGO_ENABLED = 0

# 构建标志
LDFLAGS = -s -w

.PHONY: all clean build-linux deps test help

# 默认目标
all: build-linux

# 构建 Linux 版本
build-linux: clean deps
	@echo "开始构建 $(APP_NAME) Linux 版本..."
	@mkdir -p $(LINUX_DIR)
	@CGO_ENABLED=$(CGO_ENABLED) GOOS=$(GOOS) GOARCH=$(GOARCH) \
		go build -ldflags="$(LDFLAGS)" -o $(LINUX_DIR)/$(APP_NAME) main.go
	@echo "复制配置文件和资源..."
	@cp config.yaml $(LINUX_DIR)/
	@if [ -d "resource" ]; then cp -r resource $(LINUX_DIR)/; fi
	@if [ -f "wait-for-it.sh" ]; then cp wait-for-it.sh $(LINUX_DIR)/; fi
	@echo "创建启动脚本..."
	@cp build.sh $(LINUX_DIR)/ || true
	@echo "设置执行权限..."
	@chmod +x $(LINUX_DIR)/$(APP_NAME)
	@if [ -f "$(LINUX_DIR)/start.sh" ]; then chmod +x $(LINUX_DIR)/start.sh; fi
	@echo ""
	@echo "✅ 构建完成！"
	@echo "📁 构建目录: $(LINUX_DIR)"
	@echo "🚀 可执行文件: $(LINUX_DIR)/$(APP_NAME)"
	@echo ""
	@ls -la $(LINUX_DIR)/

# 下载依赖
deps:
	@echo "下载 Go 依赖..."
	@go mod tidy
	@go mod download

# 运行测试
test:
	@echo "运行测试..."
	@go test ./...

# 清理构建文件
clean:
	@echo "清理构建目录..."
	@rm -rf $(BUILD_DIR)

# 快速构建（不清理）
quick-build:
	@echo "快速构建 $(APP_NAME)..."
	@mkdir -p $(LINUX_DIR)
	@CGO_ENABLED=$(CGO_ENABLED) GOOS=$(GOOS) GOARCH=$(GOARCH) \
		go build -ldflags="$(LDFLAGS)" -o $(LINUX_DIR)/$(APP_NAME) main.go
	@echo "✅ 快速构建完成！"

# 打包为 tar.gz
package: build-linux
	@echo "创建部署包..."
	@cd $(BUILD_DIR) && tar -czf $(APP_NAME)-linux-amd64.tar.gz linux/
	@echo "✅ 部署包已创建: $(BUILD_DIR)/$(APP_NAME)-linux-amd64.tar.gz"

# 显示帮助信息
help:
	@echo "Transport Server 构建工具"
	@echo ""
	@echo "可用命令:"
	@echo "  make build-linux  - 构建 Linux 版本（默认）"
	@echo "  make quick-build  - 快速构建（不清理）"
	@echo "  make package      - 构建并打包为 tar.gz"
	@echo "  make deps         - 下载依赖"
	@echo "  make test         - 运行测试"
	@echo "  make clean        - 清理构建文件"
	@echo "  make help         - 显示此帮助信息"
	@echo ""
	@echo "构建参数:"
	@echo "  GOOS=$(GOOS)"
	@echo "  GOARCH=$(GOARCH)"
	@echo "  CGO_ENABLED=$(CGO_ENABLED)"
